#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include "link_protocol.h"

static const char* TAG = "MAIN";

uint8_t ble_addr_type;

// Test function to demonstrate advertising
void test_advertising()
{
    ESP_LOGI(TAG, "Testing BLE advertising...");

    // Example 128-bit UUID: 12345678-1234-5678-9ABC-DEF012345678
    std::string test_uuid = "123456781234567889ABCDEF01234567";
    uint8_t test_version = 2;
    std::string device_name = "LinkPet";

    // Start advertising using C++ convenience function
    int result = ble_start_advertising_cpp(test_uuid, test_version, device_name);
    if (result == 0) {
        ESP_LOGI(TAG, "Advertising started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start advertising: %d", result);
    }
}

// Test function to demonstrate link cycle
void test_link_cycle()
{
    ESP_LOGI(TAG, "Testing Link Cycle...");

    // Example 128-bit UUID: 12345678-1234-5678-9ABC-DEF012345678
    std::string test_uuid = "ABCDEF0123456789FEDCBA9876543210";
    uint8_t test_version = 3;
    std::string device_name = "LinkCycle";

    // Start link cycle using C++ convenience function
    int result = start_link_cycle_cpp(test_uuid, test_version, device_name);
    if (result == 0) {
        ESP_LOGI(TAG, "Link cycle started successfully");
        ESP_LOGI(TAG, "Link cycle will run with 400ms periods:");
        ESP_LOGI(TAG, "  0-150ms: Scanning for devices with 128-bit UUIDs");
        ESP_LOGI(TAG, "  150-170ms: Random back-off (0-20ms)");
        ESP_LOGI(TAG, "  170-400ms: Advertising with UUID: %s", test_uuid.c_str());
    } else {
        ESP_LOGE(TAG, "Failed to start link cycle: %d", result);
    }
}

// Test function to demonstrate filtered scanning
void test_filtered_scan()
{
    ESP_LOGI(TAG, "Testing filtered BLE scan...");

    // Test with a specific UUID filter
    std::string uuid_filter = "ABCDEF";  // Look for UUIDs containing "ABCDEF"

    ESP_LOGI(TAG, "Starting scan with UUID filter: %s", uuid_filter.c_str());
    std::string scan_result = ble_scan_start_and_wait_json(uuid_filter);
    ESP_LOGI(TAG, "Scan result: %s", scan_result.c_str());
}

// Test function to demonstrate LinkCID scanning
void test_linkcid_scan()
{
    ESP_LOGI(TAG, "Testing LinkCID scanning...");

    // First, set a test LinkCID
    std::string test_linkcid = "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT";
    int result = set_linkcid(test_linkcid);
    if (result == 0) {
        ESP_LOGI(TAG, "LinkCID set successfully: %s", test_linkcid.c_str());
    } else {
        ESP_LOGE(TAG, "Failed to set LinkCID: %d", result);
        return;
    }

    // Get current LinkCID to verify
    std::string current_linkcid = get_linkcid();
    ESP_LOGI(TAG, "Current LinkCID: %s", current_linkcid.c_str());

    // Start advertising with the LinkCID
    ESP_LOGI(TAG, "Starting advertising with LinkCID...");
    result = ble_start_advertising_cpp("", 1, "LinkCID_Test");
    if (result == 0) {
        ESP_LOGI(TAG, "LinkCID advertising started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start LinkCID advertising: %d", result);
    }

    // Wait a bit for advertising to be active
    vTaskDelay(pdMS_TO_TICKS(2000));

    // Now scan for LinkCID devices
    ESP_LOGI(TAG, "Starting LinkCID scan (5 seconds)...");
    std::string scan_result = scan_get_linkcid(5000);
    ESP_LOGI(TAG, "LinkCID scan result: %s", scan_result.c_str());

    // Stop advertising
    ble_stop_advertising();
    ESP_LOGI(TAG, "LinkCID advertising stopped");
}

// Test function to demonstrate LinkCID management
void test_linkcid_management()
{
    ESP_LOGI(TAG, "Testing LinkCID management...");

    // Test different LinkCID values
    std::vector<std::string> test_cids = {
        "QmRcbuiZxkRWM1ihEMh5dfpnraRMjtubCxv4WJuBnccrCT",
        "QmYwAPJzv5CZsnA8rdHaSmKRvBoN6iq72rHZ9Qb8PBR4ML",
        "QmXoypizjW3WknFiJnKLwHCnL72vedxjQkDDP1mXWo6uco"
    };

    for (const auto& cid : test_cids) {
        ESP_LOGI(TAG, "Testing CID: %s", cid.c_str());

        int result = set_linkcid(cid);
        if (result == 0) {
            std::string retrieved_cid = get_linkcid();
            ESP_LOGI(TAG, "  Set successfully, retrieved: %s", retrieved_cid.c_str());

            // Test advertising with this CID
            result = ble_start_advertising_cpp("", 1, "TestDevice");
            if (result == 0) {
                ESP_LOGI(TAG, "  Advertising started with CID");
                vTaskDelay(pdMS_TO_TICKS(1000));
                ble_stop_advertising();
                ESP_LOGI(TAG, "  Advertising stopped");
            }
        } else {
            ESP_LOGE(TAG, "  Failed to set CID: %d", result);
        }

        vTaskDelay(pdMS_TO_TICKS(500));
    }

    // Test invalid CID
    ESP_LOGI(TAG, "Testing invalid CID (too short)...");
    int result = set_linkcid("TooShort");
    if (result != 0) {
        ESP_LOGI(TAG, "  Correctly rejected invalid CID");
    } else {
        ESP_LOGE(TAG, "  ERROR: Invalid CID was accepted!");
    }
}

extern "C" void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service

    // Set a custom sync callback that will start link cycle
    ble_hs_cfg.sync_cb = [](void) {
        ble_hs_id_infer_auto(0, &ble_addr_type);
        // Start link cycle after sync (comment out to use simple advertising)
        //test_link_cycle();
         test_advertising();  // Uncomment this and comment above to use simple advertising
    };

    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}
