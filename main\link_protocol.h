#ifndef LINK_PROTOCOL_H
#define LINK_PROTOCOL_H
#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>
//LINK PROTOCOL CONSTANTS
#define LINK_PROTOCOL_MANUFACTURER_ID_V1 { 0xFF, 0xFF, 0x4C, 0x49, 0x4E, 0x4B, 0x01 }
#define LINK_PROTOCOL_MANUFACTURER_ID_V2 { 0xFF, 0xFF, 0x4C, 0x49, 0x4E, 0x4B, 0x02 }
#define LINK_PROTOCOL_MANUFACTURER_ID_V3 { 0xFF, 0xFF, 0x4C, 0x49, 0x4E, 0x4B, 0x03 }
#ifdef __cplusplus
extern "C" {
#endif

// C-compatible Bluetooth scanning function declarations
void ble_app_scan(void);
void ble_app_on_sync(void);
void host_task(void *param);

// Filter function for manufacturer data
int is_link_protocol_device(const uint8_t *mfg_data, int mfg_data_len);

// Complete filter function for BLE devices
int ble_device_filter(const struct ble_hs_adv_fields *fields);

// BLE advertising functions
int ble_start_advertising(const uint8_t *uuid128, uint8_t version, const char *device_name);
int ble_stop_advertising(void);

// Link cycle functions
int start_link_cycle(void);
int start_link_cycle_with_config(const char* uuid128_hex, uint8_t version, const char* device_name);
int stop_link_cycle(void);
void ble_app_scan_short(int duration_ms);

#ifdef __cplusplus
}

// C++ includes (only available when compiling as C++)
#include <vector>
#include <string>
#include <mutex>

// C++ function declarations (cannot be in extern "C" block)
// UUID filter is required and cannot be empty
std::string ble_scan_start_and_wait_json(const std::string& uuid_filter);

// C++ advertising convenience functions
int ble_start_advertising_cpp(const std::string& uuid128_hex, uint8_t version, const std::string& device_name);

// C++ link cycle convenience functions
int start_link_cycle_cpp(const std::string& uuid128_hex, uint8_t version, const std::string& device_name);

// LinkCID management functions
int set_linkcid(const std::string& linkcid);
std::string get_linkcid(void);

#endif

#endif // LINK_PROTOCOL_H
